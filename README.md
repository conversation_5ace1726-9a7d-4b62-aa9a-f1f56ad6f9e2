# Chain Fox - Blockchain Security & DAO Platform

[English](README.md) | [中文](README.zh.md)

Chain Fox is a comprehensive blockchain security and decentralized autonomous organization (DAO) platform built on Solana. This repository contains the frontend application that provides users with security audit services, DAO governance features, staking mechanisms, and multi-signature wallet management.

![Project Logo](/public/logo.png)

## 🌟 Platform Overview

Chain Fox combines blockchain security auditing with decentralized governance, creating a comprehensive ecosystem where users can:

- **Security Auditing**: Automated analysis of smart contracts and blockchain projects
- **DAO Governance**: Participate in decentralized decision-making through CFX token staking
- **Credits System**: Earn and manage credits through various platform activities
- **Multi-signature Management**: Secure treasury and proposal management
- **Staking Rewards**: Earn rewards by staking CFX tokens in the DAO

### 🎨 Design Philosophy

The dynamic particle network background (NetworkParticles) represents our core mission:

- **Protecting Blockchain Data**: Each glowing particle represents a blockchain node, with interconnected lines symbolizing data flows and interactions
- **Illuminating the Dark Forest**: <PERSON> Fox acts as a guardian, scanning and securing the blockchain ecosystem, bringing clarity to complex security challenges

## 🚀 Key Features

### 🔐 Blockchain Security Auditing
- **Multi-language Support**: Rust, Solidity, Go, and other blockchain languages
- **AI-Powered Analysis**: Integration with advanced AI models for code analysis
- **Professional Reports**: Comprehensive security audit reports with PDF export
- **GitHub Integration**: Direct repository analysis and automated scanning

### 🏛️ DAO Governance System
- **CFX Token Staking**: Stake CFX tokens to participate in governance
- **Proposal Management**: Create, vote on, and execute governance proposals
- **Multi-signature Treasury**: Secure fund management with multi-sig wallets
- **Voting Power**: Staking amount determines governance influence

### 💰 Credits & Rewards System
- **Credits Economy**: Earn credits through platform participation
- **Staking Rewards**: Continuous rewards based on staking amount and duration
- **Credits Burning**: Exchange credits for CFX tokens through admin-approved process
- **UTXO Model**: Transparent credit tracking through transaction history

### 🔗 Solana Integration
- **Wallet Connection**: Support for major Solana wallets (Phantom, Solflare, etc.)
- **Smart Contracts**: Custom staking and governance contracts on Solana
- **Token Management**: CFX token (6 decimals) with 10 billion total supply
- **RPC Optimization**: Efficient blockchain interactions through Helius API

## 🛠️ Technical Architecture

### Frontend Framework
- **React 18**: Modern React with hooks and context API
- **Vite**: Lightning-fast build tool and development server
- **Tailwind CSS**: Utility-first CSS framework for rapid UI development
- **Framer Motion**: Powerful animation library for smooth transitions
- **Three.js**: 3D graphics for particle background effects

### State Management & Routing
- **React Router v7**: Client-side routing with nested routes
- **Context API**: Global state management for auth, wallet, and DAO
- **React Hooks**: Custom hooks for reusable logic

### Blockchain Integration
- **@solana/web3.js**: Solana blockchain interactions
- **Wallet Adapters**: Support for multiple Solana wallets
- **Custom Smart Contracts**: Staking and governance contracts
- **RPC Management**: Optimized RPC calls through Helius API

### Backend Services
- **Supabase**: Authentication, database, and edge functions
- **PostgreSQL**: Relational database with row-level security
- **Edge Functions**: Serverless functions for complex calculations
- **Real-time Subscriptions**: Live updates for DAO activities

### Security & Performance
- **Row-Level Security**: Database-level access control
- **JWT Authentication**: Secure token-based authentication
- **Permission Middleware**: Fine-grained access control
- **Optimized RPC**: Reduced blockchain calls and caching

## 📱 Application Features

### 🏠 Homepage & Navigation
- **Dynamic Particle Background**: Three.js-powered visual effects
- **Responsive Design**: Optimized for desktop, tablet, and mobile
- **Multi-language Support**: English and Chinese with easy language switching
- **Smooth Animations**: Framer Motion transitions throughout the app

### 🔍 Security Audit System
- **Code Analysis**: Support for Rust, Solidity, Go, and other languages
- **GitHub Integration**: Direct repository scanning and analysis
- **AI-Powered Detection**: Advanced vulnerability detection with AI
- **Professional Reports**: Comprehensive audit reports with PDF export
- **Real-time Progress**: Live updates during analysis process

### 🏛️ DAO Governance Interface
- **Staking Dashboard**: View and manage CFX token stakes
- **Proposal System**: Create, vote on, and track governance proposals
- **Multi-signature Management**: Secure treasury operations
- **Voting History**: Complete record of governance participation
- **Rewards Tracking**: Monitor staking rewards and credits earned

### 💳 Wallet & Credits Management
- **Solana Wallet Integration**: Support for Phantom, Solflare, and more
- **Balance Display**: Real-time SOL and CFX token balances
- **Credits System**: Earn, track, and burn credits for rewards
- **Transaction History**: Complete record of all platform activities
- **Secure Authentication**: Message signing for wallet verification

### 👥 Multi-signature Features
- **Treasury Management**: Secure fund management with multi-sig
- **Proposal Execution**: Multi-signature approval for important decisions
- **Signer Management**: Add/remove authorized signers
- **Transaction Tracking**: Monitor all multi-sig operations

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ and npm/yarn
- Solana wallet (Phantom, Solflare, etc.)
- Supabase account for backend services

### Installation

```bash
# Install dependencies
npm install
# or
yarn install
```

### Environment Setup

Create a `.env` file in the root directory:

```env
# Supabase Configuration
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# Solana Configuration
VITE_SOLANA_RPC_URL=your_solana_rpc_url
VITE_HELIUS_API_KEY=your_helius_api_key

# CFX Token Configuration
VITE_CFX_TOKEN_MINT=RhFVq1Zt81VvcoSEMSyCGZZv5SwBdA8MV7w4HEMpump
```

### Development

```bash
# Start development server
npm run dev
# or
yarn dev
```

The application will be available at `http://localhost:5173`

### Production Build

```bash
# Build for production
npm run build
# or
yarn build

# Preview production build
npm run preview
# or
yarn preview
```

## 📁 Project Structure

```
chain-fox-frontend/
├── src/
│   ├── components/              # Reusable UI components
│   │   ├── AuditReport/         # Security audit report components
│   │   ├── DaoPage/             # DAO governance components
│   │   ├── HomePage/            # Homepage components
│   │   ├── MultisigComponents/  # Multi-signature wallet components
│   │   ├── StakingPanel/        # Staking interface components
│   │   ├── ui/                  # Common UI components
│   │   └── common/              # Shared components
│   ├── contexts/                # React context providers
│   │   ├── AuthContext.jsx      # Authentication state management
│   │   ├── WalletContext.jsx    # Wallet connection management
│   │   └── DaoProgressContext.jsx # DAO progress tracking
│   ├── pages/                   # Page components
│   │   ├── HomePage.jsx         # Landing page
│   │   ├── DaoPage.jsx          # DAO governance page
│   │   ├── StakePage.jsx        # Staking management page
│   │   ├── MultisigPage.jsx     # Multi-signature management
│   │   ├── DetectionPage.jsx    # Security audit interface
│   │   └── ProfilePage.jsx      # User profile and credits
│   ├── services/                # Business logic and API services
│   │   ├── stakingService/      # Staking-related services
│   │   ├── multisigService.js   # Multi-signature operations
│   │   ├── creditsService.js    # Credits system management
│   │   ├── solanaRpcService.js  # Solana blockchain interactions
│   │   ├── supabase.js          # Database and authentication
│   │   └── walletConnectionManager.js # Wallet connection logic
│   ├── data/                    # Static data and configurations
│   │   ├── idl/                 # Solana program IDL files
│   │   ├── program-ids.json     # Smart contract addresses
│   │   └── explorationData.json # Platform feature data
│   ├── utils/                   # Utility functions
│   │   ├── notifications.js     # Toast notifications
│   │   ├── supabaseQueries.js   # Database query helpers
│   │   └── serverPermissionCheck.js # Permission validation
│   └── styles/                  # Custom CSS styles
├── public/
│   ├── locales/                 # Internationalization files
│   │   ├── en/                  # English translations
│   │   └── zh/                  # Chinese translations
│   └── imgs/                    # Static images and assets
├── supabase/                    # Backend configuration
│   ├── functions/               # Edge functions
│   ├── migrations/              # Database migrations
│   └── config.toml              # Supabase configuration
├── docs/                        # Documentation
│   ├── Chain-Fox-DAO-Guide.md   # DAO user guide
│   ├── staking-rewards-system-design.md # Staking system docs
│   └── credits-security-design.md # Credits system security
└── tools/                       # Development and migration tools
    ├── migrations/              # Database migration scripts
    └── credits_manager.js       # Credits management utilities
```

## 🌐 Internationalization

The application supports multiple languages using react-i18next:

### Supported Languages
- **English (en)** - Default language
- **Chinese (zh)** - Complete Chinese localization

### Translation Structure
```
public/locales/
├── en/                    # English translations
│   ├── common.json        # Common UI elements
│   ├── home.json          # Homepage content
│   ├── dao.json           # DAO governance terms
│   ├── multisig.json      # Multi-signature interface
│   └── profile.json       # User profile page
└── zh/                    # Chinese translations
    ├── common.json        # 通用界面元素
    ├── home.json          # 首页内容
    ├── dao.json           # DAO治理术语
    ├── multisig.json      # 多签界面
    └── profile.json       # 用户资料页面
```

### Adding New Languages
1. Create a new language folder in `public/locales/`
2. Copy and translate all JSON files from an existing language
3. Add the language option in `LanguageSwitcher.jsx`
4. Update the language detector configuration in `i18n.js`

## 🔧 Configuration

### Environment Variables
```env
# Supabase Backend
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your_anon_key

# Solana Network
VITE_SOLANA_RPC_URL=https://api.mainnet-beta.solana.com
VITE_HELIUS_API_KEY=your_helius_key

# Token Configuration
VITE_CFX_TOKEN_MINT=RhFVq1Zt81VvcoSEMSyCGZZv5SwBdA8MV7w4HEMpump

# Smart Contract Addresses
VITE_STAKE_PROGRAM_ID=your_stake_program_id
VITE_MULTISIG_PROGRAM_ID=your_multisig_program_id
```

### Deployment Options
- **Cloudflare Pages**: Recommended for production
- **Vercel**: Easy deployment with GitHub integration
- **Netlify**: Alternative hosting with continuous deployment
- **Self-hosted**: Deploy on your own infrastructure

> **Documentation**: [English Deployment Guide](./deploy.md) | [中文部署指南](./deploy.zh.md)

## 🔐 Security Features

### Authentication & Authorization
- **Supabase Auth**: Secure authentication with GitHub, Google, and Discord
- **Row-Level Security**: Database-level access control
- **JWT Tokens**: Secure session management
- **Whitelist System**: Admin-controlled user access
- **Permission Middleware**: Fine-grained access control

### Blockchain Security
- **Message Signing**: Wallet ownership verification
- **RPC Optimization**: Secure and efficient blockchain calls
- **Smart Contract Auditing**: Automated security analysis
- **Multi-signature**: Secure treasury management

### Data Protection
- **Encrypted Storage**: Sensitive data encryption
- **Secure Transmission**: HTTPS and secure WebSocket connections
- **Privacy Controls**: User data protection and deletion rights
- **Audit Trails**: Complete activity logging

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

**Chain Fox** - Securing the blockchain ecosystem through decentralized governance and automated security auditing.
