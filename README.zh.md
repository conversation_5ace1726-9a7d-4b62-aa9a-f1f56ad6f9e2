# Chain Fox - 区块链安全与 DAO 治理平台

[English](README.md) | [中文](README.zh.md)

Chain Fox 是一个基于 Solana 构建的综合性区块链安全和去中心化自治组织（DAO）平台。本仓库包含前端应用程序，为用户提供安全审计服务、DAO 治理功能、质押机制和多签钱包管理。

![项目标志](/public/logo.png)

## 🌟 平台概览

Chain Fox 将区块链安全审计与去中心化治理相结合，创建了一个综合生态系统，用户可以：

- **安全审计**：智能合约和区块链项目的自动化分析
- **DAO 治理**：通过质押 CFX 代币参与去中心化决策
- **积分系统**：通过各种平台活动赚取和管理积分
- **多签管理**：安全的资金库和提案管理
- **质押奖励**：通过在 DAO 中质押 CFX 代币获得奖励

### 🎨 设计理念

动态粒子网络背景（NetworkParticles）代表我们的核心使命：

- **守护区块链数据**：每个发光粒子代表一个区块链节点，相互连接的线条象征数据流和交互
- **照亮黑暗森林**：Chain Fox 作为守护者，扫描并保护区块链生态系统，为复杂的安全挑战带来清晰度


## 🚀 核心功能

### 🔐 区块链安全审计
- **多语言支持**：Rust、Solidity、Go 和其他区块链语言
- **AI 驱动分析**：集成先进 AI 模型进行代码分析
- **专业报告**：全面的安全审计报告，支持 PDF 导出
- **GitHub 集成**：直接仓库分析和自动化扫描

### 🏛️ DAO 治理系统
- **CFX 代币质押**：质押 CFX 代币参与治理
- **提案管理**：创建、投票和执行治理提案
- **多签资金库**：通过多签钱包进行安全资金管理
- **投票权重**：质押数量决定治理影响力

### 💰 积分与奖励系统
- **积分经济**：通过平台参与赚取积分
- **质押奖励**：基于质押数量和持续时间的持续奖励
- **积分销毁**：通过管理员批准流程将积分兑换为 CFX 代币
- **UTXO 模型**：通过交易历史进行透明的积分跟踪

### 🔗 Solana 集成
- **钱包连接**：支持主流 Solana 钱包（Phantom、Solflare 等）
- **智能合约**：Solana 上的自定义质押和治理合约
- **代币管理**：CFX 代币（6 位小数），总供应量 100 亿
- **RPC 优化**：通过 Helius API 进行高效的区块链交互

## 🛠️ 技术架构

### 前端框架
- **React 18**：现代 React 与 hooks 和 context API
- **Vite**：闪电般快速的构建工具和开发服务器
- **Tailwind CSS**：实用优先的 CSS 框架，快速 UI 开发
- **Framer Motion**：强大的动画库，流畅过渡效果
- **Three.js**：粒子背景效果的 3D 图形

### 状态管理与路由
- **React Router v7**：客户端路由与嵌套路由
- **Context API**：认证、钱包和 DAO 的全局状态管理
- **React Hooks**：可重用逻辑的自定义 hooks

### 区块链集成
- **@solana/web3.js**：Solana 区块链交互
- **钱包适配器**：支持多个 Solana 钱包
- **自定义智能合约**：质押和治理合约
- **RPC 管理**：通过 Helius API 优化 RPC 调用

### 后端服务
- **Supabase**：认证、数据库和边缘函数
- **PostgreSQL**：具有行级安全性的关系数据库
- **边缘函数**：复杂计算的无服务器函数
- **实时订阅**：DAO 活动的实时更新

### 安全与性能
- **行级安全性**：数据库级访问控制
- **JWT 认证**：安全的基于令牌的认证
- **权限中间件**：细粒度访问控制
- **优化 RPC**：减少区块链调用和缓存

## 📱 应用功能

### 🏠 首页与导航
- **动态粒子背景**：Three.js 驱动的视觉效果
- **响应式设计**：针对桌面、平板和移动设备优化
- **多语言支持**：中英文切换，易于语言切换
- **流畅动画**：整个应用的 Framer Motion 过渡效果

### 🔍 安全审计系统
- **代码分析**：支持 Rust、Solidity、Go 和其他语言
- **GitHub 集成**：直接仓库扫描和分析
- **AI 驱动检测**：使用 AI 进行高级漏洞检测
- **专业报告**：全面的审计报告，支持 PDF 导出
- **实时进度**：分析过程中的实时更新

### 🏛️ DAO 治理界面
- **质押仪表板**：查看和管理 CFX 代币质押
- **提案系统**：创建、投票和跟踪治理提案
- **多签管理**：安全的资金库操作
- **投票历史**：治理参与的完整记录
- **奖励跟踪**：监控质押奖励和赚取的积分

### 💳 钱包与积分管理
- **Solana 钱包集成**：支持 Phantom、Solflare 等
- **余额显示**：实时 SOL 和 CFX 代币余额
- **积分系统**：赚取、跟踪和销毁积分获得奖励
- **交易历史**：所有平台活动的完整记录
- **安全认证**：钱包验证的消息签名

### 👥 多签功能
- **资金库管理**：通过多签进行安全资金管理
- **提案执行**：重要决策的多签批准
- **签名者管理**：添加/删除授权签名者
- **交易跟踪**：监控所有多签操作

## 🚀 快速开始

### 前置要求
- Node.js 18+ 和 npm/yarn
- Solana 钱包（Phantom、Solflare 等）
- Supabase 账户用于后端服务

### 安装

```bash

# 安装依赖
npm install
# 或
yarn install
```

### 环境设置

在根目录创建 `.env` 文件：

```env
# Supabase 配置
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# Solana 配置
VITE_SOLANA_RPC_URL=your_solana_rpc_url
VITE_HELIUS_API_KEY=your_helius_api_key

# CFX 代币配置
VITE_CFX_TOKEN_MINT=RhFVq1Zt81VvcoSEMSyCGZZv5SwBdA8MV7w4HEMpump
```

### 开发

```bash
# 启动开发服务器
npm run dev
# 或
yarn dev
```

应用将在 `http://localhost:5173` 可用

### 生产构建

```bash
# 构建生产版本
npm run build
# 或
yarn build

# 预览生产构建
npm run preview
# 或
yarn preview
```

## 📁 项目结构

```
chain-fox-frontend/
├── src/
│   ├── components/              # 可重用 UI 组件
│   │   ├── AuditReport/         # 安全审计报告组件
│   │   ├── DaoPage/             # DAO 治理组件
│   │   ├── HomePage/            # 首页组件
│   │   ├── MultisigComponents/  # 多签钱包组件
│   │   ├── StakingPanel/        # 质押界面组件
│   │   ├── ui/                  # 通用 UI 组件
│   │   └── common/              # 共享组件
│   ├── contexts/                # React 上下文提供者
│   │   ├── AuthContext.jsx      # 认证状态管理
│   │   ├── WalletContext.jsx    # 钱包连接管理
│   │   └── DaoProgressContext.jsx # DAO 进度跟踪
│   ├── pages/                   # 页面组件
│   │   ├── HomePage.jsx         # 首页
│   │   ├── DaoPage.jsx          # DAO 治理页面
│   │   ├── StakePage.jsx        # 质押管理页面
│   │   ├── MultisigPage.jsx     # 多签管理
│   │   ├── DetectionPage.jsx    # 安全审计界面
│   │   └── ProfilePage.jsx      # 用户资料和积分
│   ├── services/                # 业务逻辑和 API 服务
│   │   ├── stakingService/      # 质押相关服务
│   │   ├── multisigService.js   # 多签操作
│   │   ├── creditsService.js    # 积分系统管理
│   │   ├── solanaRpcService.js  # Solana 区块链交互
│   │   ├── supabase.js          # 数据库和认证
│   │   └── walletConnectionManager.js # 钱包连接逻辑
│   ├── data/                    # 静态数据和配置
│   │   ├── idl/                 # Solana 程序 IDL 文件
│   │   ├── program-ids.json     # 智能合约地址
│   │   └── explorationData.json # 平台功能数据
│   ├── utils/                   # 实用函数
│   │   ├── notifications.js     # Toast 通知
│   │   ├── supabaseQueries.js   # 数据库查询助手
│   │   └── serverPermissionCheck.js # 权限验证
│   └── styles/                  # 自定义 CSS 样式
├── public/
│   ├── locales/                 # 国际化文件
│   │   ├── en/                  # 英文翻译
│   │   └── zh/                  # 中文翻译
│   └── imgs/                    # 静态图片和资源
├── supabase/                    # 后端配置
│   ├── functions/               # 边缘函数
│   ├── migrations/              # 数据库迁移
│   └── config.toml              # Supabase 配置
├── docs/                        # 文档
│   ├── Chain-Fox-DAO-Guide.md   # DAO 用户指南
│   ├── staking-rewards-system-design.md # 质押系统文档
│   └── credits-security-design.md # 积分系统安全
└── tools/                       # 开发和迁移工具
    ├── migrations/              # 数据库迁移脚本
    └── credits_manager.js       # 积分管理实用程序
```

## 🌐 国际化

应用使用 react-i18next 支持多种语言：

### 支持的语言
- **英语 (en)** - 默认语言
- **中文 (zh)** - 完整中文本地化

### 翻译结构
```
public/locales/
├── en/                    # 英文翻译
│   ├── common.json        # 通用 UI 元素
│   ├── home.json          # 首页内容
│   ├── dao.json           # DAO 治理术语
│   ├── multisig.json      # 多签界面
│   └── profile.json       # 用户资料页面
└── zh/                    # 中文翻译
    ├── common.json        # 通用界面元素
    ├── home.json          # 首页内容
    ├── dao.json           # DAO 治理术语
    ├── multisig.json      # 多签界面
    └── profile.json       # 用户资料页面
```

### 添加新语言
1. 在 `public/locales/` 中创建新语言文件夹
2. 从现有语言复制并翻译所有 JSON 文件
3. 在 `LanguageSwitcher.jsx` 中添加语言选项
4. 更新 `i18n.js` 中的语言检测器配置

## 🔧 配置

### 环境变量
```env
# Supabase 后端
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your_anon_key

# Solana 网络
VITE_SOLANA_RPC_URL=https://api.mainnet-beta.solana.com
VITE_HELIUS_API_KEY=your_helius_key

# 代币配置
VITE_CFX_TOKEN_MINT=RhFVq1Zt81VvcoSEMSyCGZZv5SwBdA8MV7w4HEMpump

# 智能合约地址
VITE_STAKE_PROGRAM_ID=your_stake_program_id
VITE_MULTISIG_PROGRAM_ID=your_multisig_program_id
```

### 部署选项
- **Cloudflare Pages**：推荐用于生产环境
- **Vercel**：与 GitHub 集成的简易部署
- **Netlify**：具有持续部署的替代托管
- **自托管**：在您自己的基础设施上部署

> **文档**：[中文部署指南](./deploy.zh.md) | [English Deployment Guide](./deploy.md)

## 🔐 安全功能

### 认证与授权
- **Supabase 认证**：支持 GitHub、Google 和 Discord 的安全认证
- **行级安全性**：数据库级访问控制
- **JWT 令牌**：安全的会话管理
- **白名单系统**：管理员控制的用户访问
- **权限中间件**：细粒度访问控制

### 区块链安全
- **消息签名**：钱包所有权验证
- **RPC 优化**：安全高效的区块链调用
- **智能合约审计**：自动化安全分析
- **多重签名**：安全的资金库管理

### 数据保护
- **加密存储**：敏感数据加密
- **安全传输**：HTTPS 和安全 WebSocket 连接
- **隐私控制**：用户数据保护和删除权限
- **审计跟踪**：完整的活动日志记录

## 🚧 开发路线图

### 第一阶段：核心平台（已完成）
- ✅ 安全审计系统
- ✅ DAO 治理框架
- ✅ 质押机制
- ✅ 多签钱包集成
- ✅ 积分系统

### 第二阶段：增强功能（进行中）
- 🔄 高级审计算法
- 🔄 移动应用程序
- 🔄 API 市场
- 🔄 社区功能
- 🔄 高级分析

### 第三阶段：生态系统扩展（计划中）
- 📋 跨链支持
- 📋 企业功能
- 📋 第三方集成
- 📋 高级治理工具
- 📋 机构功能

## 🤝 贡献

我们欢迎社区贡献！以下是您可以帮助的方式：

### 开始贡献
1. Fork 仓库
2. 克隆您的 fork：`git clone https://github.com/your-username/chain-fox-frontend.git`
3. 创建功能分支：`git checkout -b feature/amazing-feature`
4. 进行更改并彻底测试
5. 提交清晰的消息：`git commit -m 'Add: 新功能描述'`
6. 推送到您的分支：`git push origin feature/amazing-feature`
7. 创建 Pull Request

### 开发指南
- 遵循现有的代码风格和约定
- 编写清晰、描述性的提交消息
- 为新功能添加测试
- 根据需要更新文档
- 确保所有测试在提交前通过

### 贡献领域
- 🐛 错误修复和改进
- 🌟 新功能和增强
- 📚 文档改进
- 🌐 翻译和本地化
- 🎨 UI/UX 改进
- ⚡ 性能优化

## 📄 许可证

本项目根据 MIT 许可证授权 - 有关详细信息，请参阅 [LICENSE](LICENSE) 文件。

## 📞 支持与社区

- **GitHub Issues**：报告错误和请求功能
- **文档**：`/docs` 文件夹中的综合指南
- **社区**：加入我们的 Discord 进行讨论
- **Twitter**：关注 [@1379hash](https://twitter.com/1379hash) 获取更新

---

**Chain Fox** - 通过去中心化治理和自动化安全审计保护区块链生态系统。
